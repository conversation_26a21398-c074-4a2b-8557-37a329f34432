import * as vscode from "vscode"
import { ProviderSettingsManager } from "../core/config/ProviderSettingsManager"

// Mock dependencies
vitest.mock("vscode", () => ({
	workspace: {
		getConfiguration: vitest.fn(),
	},
	ConfigurationTarget: {
		Global: 1,
		Workspace: 2,
		WorkspaceFolder: 3,
	},
}))

describe("Completion Settings Migration", () => {
	let mockContext: vscode.ExtensionContext
	let providerSettingsManager: ProviderSettingsManager
	let mockWorkspaceConfig: any

	beforeEach(() => {
		// Clear all mocks
		vitest.clearAllMocks()

		// Mock workspace configuration
		mockWorkspaceConfig = {
			get: vitest.fn(),
			update: vitest.fn().mockResolvedValue(undefined),
		}

		vitest.mocked(vscode.workspace.getConfiguration).mockReturnValue(mockWorkspaceConfig)

		// Mock extension context
		mockContext = {
			globalStorageUri: { fsPath: "/mock/storage" },
			globalState: {
				get: vitest.fn(),
				update: vitest.fn().mockResolvedValue(undefined),
			},
			secrets: {
				get: vitest.fn().mockResolvedValue(undefined),
				store: vitest.fn().mockResolvedValue(undefined),
			},
			setValue: vitest.fn().mockResolvedValue(undefined),
		} as any

		providerSettingsManager = new ProviderSettingsManager(mockContext)
	})

	it("should migrate completion settings from VS Code configuration", async () => {
		// Mock VS Code configuration values
		mockWorkspaceConfig.get.mockImplementation((key: string) => {
			switch (key) {
				case "completion.debounce_time":
					return 1000
				case "completion.completion_number":
					return 2
				case "completion.inlineCompletion_granularity":
					return "单行"
				case "completion.multiple_line_Completion":
					return "触发补全"
				case "completion.max_tokens_completion":
					return 128
				default:
					return undefined
			}
		})

		// Mock provider profiles without completion migration
		const mockProviderProfiles = {
			currentApiConfigName: "default",
			apiConfigs: { default: { id: "test-id" } },
			modeApiConfigs: {},
			migrations: {
				rateLimitSecondsMigrated: true,
				diffSettingsMigrated: true,
				openAiHeadersMigrated: true,
				consecutiveMistakeLimitMigrated: true,
				todoListEnabledMigrated: true,
				completionMigrated: false, // This should trigger migration
			},
		}

		// Mock the load method to return our test data
		vitest.spyOn(providerSettingsManager as any, "load").mockResolvedValue(mockProviderProfiles)
		vitest.spyOn(providerSettingsManager as any, "store").mockResolvedValue(undefined)

		// Initialize the provider settings manager (this should trigger migration)
		await providerSettingsManager.initialize()

		// Verify that VS Code configuration was read
		expect(mockWorkspaceConfig.get).toHaveBeenCalledWith("completion.debounce_time")
		expect(mockWorkspaceConfig.get).toHaveBeenCalledWith("completion.completion_number")
		expect(mockWorkspaceConfig.get).toHaveBeenCalledWith("completion.inlineCompletion_granularity")
		expect(mockWorkspaceConfig.get).toHaveBeenCalledWith("completion.multiple_line_Completion")
		expect(mockWorkspaceConfig.get).toHaveBeenCalledWith("completion.max_tokens_completion")

		// Verify that internal settings were updated
		expect(mockContext.setValue).toHaveBeenCalledWith("completionDebounceTime", 1000)
		expect(mockContext.setValue).toHaveBeenCalledWith("completionNumber", 2)
		expect(mockContext.setValue).toHaveBeenCalledWith("inlineCompletionGranularity", "单行")
		expect(mockContext.setValue).toHaveBeenCalledWith("multipleLineCompletion", "触发补全")
		expect(mockContext.setValue).toHaveBeenCalledWith("maxTokensCompletion", 128)

		// Verify that VS Code configuration was cleared
		expect(mockWorkspaceConfig.update).toHaveBeenCalledWith(
			"completion.debounce_time",
			undefined,
			vscode.ConfigurationTarget.Global,
		)
		expect(mockWorkspaceConfig.update).toHaveBeenCalledWith(
			"completion.completion_number",
			undefined,
			vscode.ConfigurationTarget.Global,
		)
		expect(mockWorkspaceConfig.update).toHaveBeenCalledWith(
			"completion.inlineCompletion_granularity",
			undefined,
			vscode.ConfigurationTarget.Global,
		)
		expect(mockWorkspaceConfig.update).toHaveBeenCalledWith(
			"completion.multiple_line_Completion",
			undefined,
			vscode.ConfigurationTarget.Global,
		)
		expect(mockWorkspaceConfig.update).toHaveBeenCalledWith(
			"completion.max_tokens_completion",
			undefined,
			vscode.ConfigurationTarget.Global,
		)
	})

	it("should not migrate if already migrated", async () => {
		// Mock provider profiles with completion already migrated
		const mockProviderProfiles = {
			currentApiConfigName: "default",
			apiConfigs: { default: { id: "test-id" } },
			modeApiConfigs: {},
			migrations: {
				rateLimitSecondsMigrated: true,
				diffSettingsMigrated: true,
				openAiHeadersMigrated: true,
				consecutiveMistakeLimitMigrated: true,
				todoListEnabledMigrated: true,
				completionMigrated: true, // Already migrated
			},
		}

		// Mock the load method to return our test data
		vitest.spyOn(providerSettingsManager as any, "load").mockResolvedValue(mockProviderProfiles)
		vitest.spyOn(providerSettingsManager as any, "store").mockResolvedValue(undefined)

		// Initialize the provider settings manager
		await providerSettingsManager.initialize()

		// Verify that VS Code configuration was NOT read (migration skipped)
		expect(mockWorkspaceConfig.get).not.toHaveBeenCalledWith("completion.debounce_time")
		expect(mockContext.setValue).not.toHaveBeenCalledWith("completionDebounceTime", expect.anything())
	})

	it("should handle migration errors gracefully", async () => {
		// Mock VS Code configuration to throw an error
		mockWorkspaceConfig.get.mockImplementation(() => {
			throw new Error("Configuration error")
		})

		// Mock provider profiles without completion migration
		const mockProviderProfiles = {
			currentApiConfigName: "default",
			apiConfigs: { default: { id: "test-id" } },
			modeApiConfigs: {},
			migrations: {
				rateLimitSecondsMigrated: true,
				diffSettingsMigrated: true,
				openAiHeadersMigrated: true,
				consecutiveMistakeLimitMigrated: true,
				todoListEnabledMigrated: true,
				completionMigrated: false,
			},
		}

		// Mock the load method to return our test data
		vitest.spyOn(providerSettingsManager as any, "load").mockResolvedValue(mockProviderProfiles)
		vitest.spyOn(providerSettingsManager as any, "store").mockResolvedValue(undefined)

		// Mock console.error to verify error handling
		const consoleSpy = vitest.spyOn(console, "error").mockImplementation(() => {})

		// Initialize should not throw even if migration fails
		await expect(providerSettingsManager.initialize()).resolves.not.toThrow()

		// Verify error was logged
		expect(consoleSpy).toHaveBeenCalledWith(
			expect.stringContaining("[MigrateCompletionSettings] Failed to migrate completion settings:"),
			expect.any(Error),
		)

		consoleSpy.mockRestore()
	})
})

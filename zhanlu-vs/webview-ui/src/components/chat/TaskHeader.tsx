import { memo, useRef, useState } from "react"
import { useWindowSize } from "react-use"
import { useTranslation } from "react-i18next"
import { VSCodeBadge } from "@vscode/webview-ui-toolkit/react"
import { CloudUpload, CloudDownload, FoldVertical } from "lucide-react"

import type { ClineMessage } from "@roo-code/types"

import { getModelMaxOutputTokens } from "@roo/api"

import { formatLargeNumber } from "@src/utils/format"
import { cn } from "@src/lib/utils"
import { Button, StandardTooltip } from "@src/components/ui"
import { useExtensionState } from "@src/context/ExtensionStateContext"
import { useSelectedModel } from "@src/components/ui/hooks/useSelectedModel"

import { CopyButton } from "../history/CopyButton"
import Thumbnails from "../common/Thumbnails"
import AIGeneratedWarning from "../common/AIGeneratedWarning"

import { TaskActions } from "./TaskActions"
// import { ShareButton } from "./ShareButton"
import { ContextWindowProgress } from "./ContextWindowProgress"
import { Mention } from "./Mention"
import { TodoListDisplay } from "./TodoListDisplay"

export interface TaskHeaderProps {
	task: ClineMessage
	tokensIn: number
	tokensOut: number
	cacheWrites?: number
	cacheReads?: number
	totalCost: number
	contextTokens: number
	buttonsDisabled: boolean
	handleCondenseContext: (taskId: string) => void
	onClose: () => void
	todos?: any[]
	jobId?: string // 新增：支持显示JobId（用于Remote Agent）
	remoteTaskId?: string // 新增：支持显示远程TaskId（用于Remote Agent）
}

const TaskHeader = ({
	task,
	tokensIn,
	tokensOut,
	cacheWrites,
	cacheReads,
	totalCost,
	contextTokens,
	buttonsDisabled,
	handleCondenseContext,
	onClose,
	todos,
	jobId,
	remoteTaskId,
}: TaskHeaderProps) => {
	const { t } = useTranslation()
	const { apiConfiguration, currentTaskItem } = useExtensionState()
	const { id: modelId, info: model } = useSelectedModel(apiConfiguration)
	const [isTaskExpanded, setIsTaskExpanded] = useState(false)

	const textContainerRef = useRef<HTMLDivElement>(null)
	const textRef = useRef<HTMLDivElement>(null)
	const contextWindow = model?.contextWindow || 1

	const { width: windowWidth } = useWindowSize()

	const condenseButton = (
		<StandardTooltip content={t("chat:task.condenseContext")}>
			<button
				disabled={buttonsDisabled}
				onClick={() => {
					const taskIdForCondense = remoteTaskId || currentTaskItem?.id
					if (taskIdForCondense) {
						handleCondenseContext(taskIdForCondense)
					}
				}}
				className="shrink-0 min-h-[20px] min-w-[20px] p-[2px] cursor-pointer disabled:cursor-not-allowed opacity-85 hover:opacity-100 bg-transparent border-none rounded-md">
				<FoldVertical size={16} />
			</button>
		</StandardTooltip>
	)

	const hasTodos = todos && Array.isArray(todos) && todos.length > 0

	return (
		<div className="py-2 px-3">
			<div
				className={cn(
					"p-2.5 flex flex-col gap-1.5 relative z-1 border",
					hasTodos ? "rounded-t-xs border-b-0" : "rounded-xs",
					isTaskExpanded
						? "border-vscode-panel-border text-vscode-foreground"
						: "border-vscode-panel-border/80 text-vscode-foreground/80",
				)}>
				<div className="flex justify-between items-center gap-2">
					<div
						className="flex items-center cursor-pointer -ml-0.5 select-none grow min-w-0"
						onClick={() => setIsTaskExpanded(!isTaskExpanded)}>
						<div className="flex items-center shrink-0">
							<span className={`codicon codicon-chevron-${isTaskExpanded ? "down" : "right"}`}></span>
						</div>
						<div className="ml-1.5 whitespace-nowrap overflow-hidden text-ellipsis grow min-w-0">
							<span className="font-bold">
								{t("chat:task.title")}
								{!isTaskExpanded && ":"}
							</span>
							{!isTaskExpanded && (
								<span className="ml-1">
									<Mention text={task.text} />
								</span>
							)}
						</div>
					</div>
					<StandardTooltip content={t("chat:task.closeAndStart")}>
						<Button variant="ghost" size="icon" onClick={onClose} className="shrink-0 w-5 h-5">
							<span className="codicon codicon-close" />
						</Button>
					</StandardTooltip>
				</div>
				{/* Collapsed state: Track context and cost if we have any */}
				{!isTaskExpanded && (
					<div className="flex flex-col gap-1">
						{contextWindow > 0 && (
							<div className={`w-full flex flex-row gap-1 h-auto`}>
								<ContextWindowProgress
									contextWindow={contextWindow}
									contextTokens={contextTokens || 0}
									maxTokens={
										model
											? getModelMaxOutputTokens({
													modelId,
													model,
													settings: apiConfiguration,
												})
											: undefined
									}
								/>
								{condenseButton}
								{/* <ShareButton item={currentTaskItem} disabled={buttonsDisabled} /> */}
								{!!totalCost && <VSCodeBadge>${totalCost.toFixed(2)}</VSCodeBadge>}
							</div>
						)}
						<AIGeneratedWarning />
					</div>
				)}
				{/* Expanded state: Show task text and images */}
				{isTaskExpanded && (
					<>
						<div
							ref={textContainerRef}
							className="-mt-0.5 text-vscode-font-size break-words break-anywhere relative">
							<div
								ref={textRef}
								className="overflow-auto max-h-80 whitespace-pre-wrap break-words break-anywhere"
								style={{
									scrollbarWidth: "thin",
									scrollbarColor: "var(--vscode-scrollbarSlider-background) transparent",
								}}>
								<Mention text={task.text} />
							</div>
						</div>
						{task.images && task.images.length > 0 && <Thumbnails images={task.images} />}

						<div className="flex flex-col gap-1">
							{isTaskExpanded && contextWindow > 0 && (
								<div
									className={`w-full flex ${windowWidth < 400 ? "flex-col" : "flex-row"} gap-1 h-auto`}>
									<div className="flex items-center gap-1 flex-shrink-0">
										<span className="font-bold" data-testid="context-window-label">
											{t("chat:task.contextWindow")}
										</span>
									</div>
									<ContextWindowProgress
										contextWindow={contextWindow}
										contextTokens={contextTokens || 0}
										maxTokens={
											model
												? getModelMaxOutputTokens({
														modelId,
														model,
														settings: apiConfiguration,
													})
												: undefined
										}
									/>
									{condenseButton}
								</div>
							)}
							{/* JobId and TaskID display */}
							{jobId && (
								<div className="flex justify-between items-center h-[20px]">
									<div className="flex items-center gap-1">
										<span className="font-bold">Job ID:</span>
										<span className="font-mono text-xs">{jobId}</span>
									</div>
									<CopyButton itemTask={jobId} title="复制 Job ID" />
								</div>
							)}
							{(remoteTaskId || currentTaskItem?.id) && (
								<div className="flex justify-between items-center h-[20px]">
									<div className="flex items-center gap-1">
										<span className="font-bold">Task ID:</span>
										<span className="font-mono text-xs">{remoteTaskId || currentTaskItem?.id}</span>
									</div>
									<CopyButton
										itemTask={remoteTaskId || currentTaskItem?.id || ""}
										title={t("chat:task.copyId")}
									/>
								</div>
							)}
							<div className="flex justify-between items-center h-[20px]">
								<div className="flex items-center gap-1 flex-wrap">
									<span className="font-bold">{t("chat:task.tokens")}</span>
									{typeof tokensIn === "number" && tokensIn > 0 && (
										<span className="flex items-center gap-0.5">
											<i className="codicon codicon-arrow-up text-xs font-bold" />
											{formatLargeNumber(tokensIn)}
										</span>
									)}
									{typeof tokensOut === "number" && tokensOut > 0 && (
										<span className="flex items-center gap-0.5">
											<i className="codicon codicon-arrow-down text-xs font-bold" />
											{formatLargeNumber(tokensOut)}
										</span>
									)}
								</div>
								{!totalCost && currentTaskItem && (
									<TaskActions item={currentTaskItem} buttonsDisabled={buttonsDisabled} />
								)}
							</div>

							{((typeof cacheReads === "number" && cacheReads > 0) ||
								(typeof cacheWrites === "number" && cacheWrites > 0)) && (
								<div
									className={`flex items-center gap-1 flex-wrap ${windowWidth < 400 ? "h-auto" : "h-[20px]"}`}>
									<span className="font-bold">{t("chat:task.cache")}</span>
									{typeof cacheWrites === "number" && cacheWrites > 0 && (
										<span className="flex items-center gap-0.5">
											<CloudUpload size={16} />
											{formatLargeNumber(cacheWrites)}
										</span>
									)}
									{typeof cacheReads === "number" && cacheReads > 0 && (
										<span className="flex items-center gap-0.5">
											<CloudDownload size={16} />
											{formatLargeNumber(cacheReads)}
										</span>
									)}
								</div>
							)}

							{!!totalCost && (
								<div
									className={`w-full flex ${windowWidth < 400 ? "flex-col" : "flex-row justify-between"} gap-1 ${windowWidth < 400 ? "h-auto" : "h-[20px] items-center"}`}>
									<div className="flex items-center gap-1">
										<span className="font-bold">{t("chat:task.apiCost")}</span>
										<span>${totalCost?.toFixed(2)}</span>
									</div>
									{currentTaskItem && (
										<TaskActions item={currentTaskItem} buttonsDisabled={buttonsDisabled} />
									)}
								</div>
							)}
							<AIGeneratedWarning />
						</div>
					</>
				)}
			</div>
			<TodoListDisplay todos={todos ?? (task as any)?.tool?.todos ?? []} />
		</div>
	)
}

export default memo(TaskHeader)
